{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "dev": "cross-env VUE_APP_STAGE=dev vue-cli-service serve", "build": "cross-env  VUE_APP_STAGE=prod vue-cli-service build --dest ../public/dist", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.26.1", "caniuse-lite": "^1.0.30001618", "core-js": "^3.6.5", "cross-env": "^7.0.3", "vue": "^2.6.11", "vue-excel-export": "^0.1.3", "vue-router": "^3.2.0", "vuetify": "^2.6.0", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.15", "@vue/cli-plugin-eslint": "~4.5.15", "@vue/cli-plugin-router": "~4.5.15", "@vue/cli-plugin-unit-mocha": "~4.5.15", "@vue/cli-plugin-vuex": "~4.5.15", "@vue/cli-service": "~4.5.15", "@vue/eslint-config-prettier": "^6.0.0", "@vue/test-utils": "^1.0.3", "babel-eslint": "^10.1.0", "chai": "^4.1.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^6.2.2", "prettier": "^2.2.1", "sass": "~1.32.0", "sass-loader": "^10.0.0", "vue-cli-plugin-vuetify": "~2.4.8", "vue-template-compiler": "^2.6.11", "vuetify-loader": "^1.7.0"}, "license": "MIT"}