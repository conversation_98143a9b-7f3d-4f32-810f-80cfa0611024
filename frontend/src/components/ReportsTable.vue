<template>
  <v-card>
    <v-toolbar flat color="indigo darken-4">
      <v-toolbar-title class="white--text">Files Reports</v-toolbar-title>
      <v-divider class="mx-4" inset vertical></v-divider>
      <v-btn icon @click.prevent="fetchReports" class="white--text">
        <v-icon>mdi-file-refresh</v-icon>
      </v-btn>
      <v-spacer></v-spacer>
      <v-btn class="ma-2" color="success" dark @click="downloadReports">
        Download Reports
        <v-icon right dark>mdi-cloud-download</v-icon>
      </v-btn>
    </v-toolbar>
    
    <v-data-table
      :headers="headers"
      :items="reportdata"
      class="elevation-1"
      :loading="loading"
      :loading-text="loading_text"
      v-if="!hideFilesTables"
      :footer-props="{
        'items-per-page-options': [10, 20, 30, 40, 50, 100],
      }"
      :items-per-page="30"
      :search="search"
    >
      <template v-slot:[`item.createdAt`]="{ item }">{{ formatDate(item.createdAt) }}</template>
      <template v-slot:[`item.updatedAt`]="{ item }">{{ formatDate(item.updatedAt) }}</template>
      <template v-slot:no-data>
        <v-btn color="primary" @click="fetchReports">Refresh</v-btn>
      </template>
    </v-data-table>
  </v-card>
</template>
  
<script>

import UploadService from "@/services/UploadService.js";

export default {
  name: "ReportsTable",
  data: () => ({
    loading: false,
    loading_text: "Loading... Please wait",
    reportdata: [],
    search: "",
    hideFilesTables: false,
    headers: [
      { text: "ID", align: "start", sortable: false, value: "id" },
      { text: "Loan Id", value: "accountId" },
      { text: "Date Report Generated", value: "createdAt" },
      { text: "Status", value: "status" },
      { text: "Email Attached", value: "email_attached" },
      { text: "Error Reason", value: "errorReason" },
      { text: "Environment", value: "environment", sortable: false },
      { text: "Subsidiary", value: "sub", sortable: false },
    ],
  }),
  methods: {
    fetchReports() {
      this.loading = true;
      UploadService.getReports().then((data) => {
        this.reportdata = data;
        this.loading = false;
      });
    },
    formatDate(date) {
      return date.substring(0, 10);
    },
    downloadReports() {
      // Add logic for downloading reports
    },
  },
  mounted() {
    this.fetchReports();
  },
};
</script>

<style scoped>
.v-toolbar-title {
  font-weight: 600;
}
.v-btn {
  color: #fff !important;
}
.v-data-table {
  background-color: #f5f5f5;
}
.v-data-table .v-data-footer {
  border-top: 1px solid #ddd;
}
</style>
