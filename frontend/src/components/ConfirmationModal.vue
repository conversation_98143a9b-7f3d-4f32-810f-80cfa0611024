<template>
  <v-row justify="space-around">
    <v-col cols="auto">
      <v-dialog 
       v-model= "dialog"
        transition="dialog-bottom-transition"
        max-width="600"
      >

          <v-card>
            <v-toolbar
              color="rgb(33,59,148)"
              dark
            > Delete Records ? </v-toolbar>
            <v-card-text>
              <div class="text-h4 pa-12">
                {{message}}
              </div>
            </v-card-text>
            <v-card-actions class="justify-end">
                <v-btn
                text
                @click="deleteConfirm"
              >Yes</v-btn>
              <v-btn
                text
                @click="closeModal"
              >No</v-btn>

            </v-card-actions>
          </v-card>
    
      </v-dialog>
    </v-col>

  </v-row>
</template>

<script>
  export default {
    props:["dialog", "message"],
    emits:["close", 'yes'],
    data () {
      return {
        
      }
    },

    methods:{
        closeModal(){
            this.$emit('close', false);
        },
        deleteConfirm(){
            this.$emit('yes', false);
        }
    }
  }
</script>