// import axios from "axios";

// class DemandLetter {
//   constructor() {}

//   static async demandLetter() {
//     const reports = await axios.post("/pclDeceasedletter");
//     return reports.data;
//   }
// }

// export default DemandLetter;


const axios = require('axios');

class DemandLetter {
  constructor() {}

  static async demandLetter() {
    const reports = await axios.post("/pclDeceasedletter"); 
    return reports.data;
  }
}

module.exports = DemandLetter;