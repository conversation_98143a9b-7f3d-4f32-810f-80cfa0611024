const ClientService = require("../services/clientService");
const fs = require("fs/promises");
const dd = require("../utils/dd");
const pdfMake = require("../utils/pdf");
const getConfig = require("../utils/config");
const extractMambuUser = require("../utils/extractMambuUser");

const controller = {};

controller.pclDemandletter = (app) =>
  async function (req, res, next) {
    // console.log(req.body);

    let mambuObj = {};
    if (req.headers.mambuuser) {
      mambuObj = extractMambuUser(req.headers.mambuuser);
    }

    const loanID = req.body.loanID || mambuObj.loanID;
    // console.log("loanID", loanID);

    const { mambu_env = "sandbox", sub = "platinumkenya" } =
      req?.mambuData || {
        mambu_env: "sandbox",
        sub: "platinumkenya",
        source: "ui",
      };

    const config = getConfig({
      mambu_env,
      sub,
    });

    // console.log({
    //   config,
    //   mambu_env,
    //   sub,
    // });

    if (!loanID) {
      return res.status(400).json({ message: "LoanID must be provided" });
    }

    try {
      const loanAccount = await new ClientService(config).getLoanById(loanID);
      // console.log("loanAccount", loanAccount);

      const date = new Date();
      const formattedDate = date.toISOString().split("T")[0];
      // console.log(formattedDate);

      const currentYear = new Date().getFullYear();
      // console.log(currentYear);
      const {
        _ads001,
        _Employer_Details_Clients,
        encodedKey,
        id,
        activationDate,
        firstName,
        middleName,
        lastName,
        mobilePhone,
        emailAddress = null,
      } = await new ClientService(config).getClientById(
        loanAccount.accountHolderKey
      );
      // console.log({ _ads001, _Employer_Details_Clients, encodedKey, id, activationDate, firstName, middleName, lastName, mobilePhone, emailAddress });

      const fullName = `${firstName}${middleName ? " " + middleName : ""} ${lastName}`.trim();

      const payload = {
        loan_account: loanAccount.id,
        current_year: currentYear,
        full_name: fullName,
        Email_Address: emailAddress || "N/A",
        Phone_Number: mobilePhone || "N/A",
        Postal_Address: _ads001 ? _ads001["POSTAL001"] : "N/A",
        Employer_Name: _Employer_Details_Clients ? _Employer_Details_Clients["EN001"] : "N/A",
        client_id: id,
        activation_date: new Date(activationDate).toISOString().split("T")[0],
        formattedDate: formattedDate,
      };

      // Determine if  this custom fied is updated to this its under loanAccount  "_CTT001": {
      // "Employerrootcause": "Deceased",
      
      // if custom fied is "Deceased" generate the pdf
      // if not return a message that custom fied is not updated to "Deceased"

      if (loanAccount["_CTT001"]?.Employerrootcause !== "Deceased") {
        return res.json({
          message: "Custom field is not updated to Deceased"
        })
      }
      
      // generate html from dd
      const html = dd(payload);

      const file_name = "DeceasedLetter" + "_" + fullName + ".pdf";
      const { pdf_file_path } = await pdfMake(html, file_name)
      // console.log({
      //   pdf_file_path
      // })

      const attachPdf = await new ClientService(config).attachPdf({
        userId: loanID,
        fileName: file_name,
        title: "Deceased Letters",
        file_path: pdf_file_path
      });

      // console.log({
      //   attachPdf,
      //   loanID
      // })

      if (!attachPdf.hasValue) {
        return res.json({
          message: "Pdf file attachement failed",
          response: attachPdf
        })

      }

      return res.json({
        message: "Pdf file attached",
        response: attachPdf
      })


    } catch (error) {
      next(error);
    }
  };

module.exports = controller;
