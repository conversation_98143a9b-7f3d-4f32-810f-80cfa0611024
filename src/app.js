const express = require("express");
const morgan = require("morgan");
const helmet = require("helmet");
const cors = require("cors");
const session = require("./utils/session");
require("dotenv").config();
const path = require("path");
const readSession = require('./utils/readSession');

const api = require("./routes/demandletter.js");

const middlewares = require("./middleware");

const fronteend = path.join(__dirname, "../public/dist");

// console.log({fronteend})

const app = express();

app.use(morgan("dev"));
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(express.static(fronteend));
app.use(express.static("public"));


const sessions = {}

app.get("/pcldeceasedletter/app-definition/", async (req, res) => {
  res.status(200).sendFile(path.join(__dirname, "../public/dev.xml"));
});

app.post("/pcldeceasedletter/ui", (req, res, next) => {
  // console.log(req.body);
  const _signed = req.body.signed_request;
  session(_signed);
  req.isrequestvalid = true
  const signedpart1 = _signed.split(".")[0]
  req.signedToken = signedpart1
  req.method = "GET";
  next();
});


app.use(
  "/pcldeceasedletter/ui",
  function (req, res, next) { 
    if (req.isrequestvalid) {
      let expiry = new Date().getTime();
      expiry += 1000 * 2;
      sessions[req.signedToken] = { expiresIn: expiry };
    }
    const mambuUser = JSON.parse(readSession() || JSON.stringify({ session: '' })).session;
    const sess = sessions[mambuUser.split('.')[0]];
    // console.log("sess",sess)
    const mambuUserSession = req.header('mambuUser');
    // console.log("mambuUserSession",mambuUserSession)
    if (sess || mambuUserSession) {
      if (sess && sess.expiresIn > new Date().getTime() || mambuUserSession) {
        next();
      } else {
        // Send the custom HTML page for unauthorized access
        return res.status(404).sendFile(path.join(__dirname, "../public/dist/error404.html"));
      }
    } else {
      // Send the custom HTML page for unauthorized access
      return res.status(404).sendFile(path.join(__dirname, "../public/dist/error404.html"));
    }
  },
  
  express.static(path.join(__dirname, "../public/dist"))
);
app.use("/pcldeceasedletter/v1", api(app));

app.use(middlewares.notFound);
app.use(middlewares.errorHandler);

module.exports = app;
