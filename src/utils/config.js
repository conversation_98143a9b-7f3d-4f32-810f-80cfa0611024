require("dotenv").config();

const config = {
  platinumkenya: {
    sandbox: {
      baseUrl: "https://platinumkenya.sandbox.mambu.com/api",
      apiKey: process.env.mambu_api_key,
    },
    production: {
      apiKey: process.env.mambu_api_key,
      baseUrl: "https://platinumkenya.mambu.com/api",
    },

  },

};

function getConfig({ sub = "platinumkenya", mambu_env = "sandbox" }) {
  const val = {
    ...config[sub][mambu_env],
  };

  return val;
}

module.exports = getConfig;
