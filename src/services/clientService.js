const axios = require("axios").default;

const FormData = require("form-data");
const fs = require("fs");
const path = require("path");

class ClientService {
  constructor({ baseUrl, apiKey }) {
    this.apiKey = apiKey;
    this.baseURL = baseUrl;
    this.mambuClient = axios.create({
      baseURL: baseUrl,
      headers: {
        Accept: "application/vnd.mambu.v2+json",
        ApiKey: apiKey,
      },
    });
  }


  async getLoanById(id) {
    const loan = await this.mambuClient.get(`/loans/${id}/?detailsLevel=FULL`);
    return loan.data;
  }


  async getClientById(id) {
    const client = await this.mambuClient.get(`/clients/${id}/?detailsLevel=FULL`);
    // console.log(client.data);
    return client.data;
  }


  // async searchLoans(accountHolderId, value= 'PL') { 
  //   const searchData = {

  //     filterCriteria: [
  //       {
  //         field: 'accountHolderKey',
  //         operator: 'EQUALS',
  //         value: accountHolderId
  //       },

  //       {
  //         field: 'accountState',
  //         operator: 'IN',
  //         values: ['ACTIVE', 'ACTIVE_IN_ARREARS' ]
  //       },

  //       {
  //         field: 'loanName',
  //         operator: 'STARTS_WITH',
  //         value: value,
  //       },
 
  //     ]
  //   };

  //   try {
  //     const loans = await axios.post(`/loans:search?detailsLevel=FULL`, searchData, {

  //       headers: {
  //         Accept: "application/vnd.mambu.v2+json"
  //       }

  //     });
      
  //     return loans.data;
  //   } catch (error) {
  //     console.log(error.response.data);
  //     throw new Error(error);
  //   }
  // }
   
  /**
   * @param(object) data
   * @param(Number) data.userId client mambu id
   * @param(String) data.file_path location of the file
   * @param(String) data.title  pdf title
   * 
   */
  
  attachPdf({ userId, file_path, title, owner = "LOAN_ACCOUNT" }) {
    const formData = new FormData();
    formData.append("ownerType", owner);
    formData.append("id", userId);
    formData.append("name", title);
    // formData.append("notes", notes);
    formData.append("file", fs.createReadStream(path.join(file_path)));

    return axios({
      method: "post",
      url: this.baseURL + "/documents",
      data: formData,
      headers: {
        "Content-Type": `multipart/form-data; boundary=${formData._boundary}`,
        Accept: "application/vnd.mambu.v2+json",
        ApiKey: this.apiKey,
      },
    })
      .then((res) => {
        return {
          hasValue: true,
          data: res.data,
        };
      })
      .catch((error) => {
        const failedObj = error.response.data.errors[0];
        // console.log({
        //   failedObj,
        // });
        return {
          hasValue: false,
          data: {
            errorReason: failedObj.errorReason ?? "unknown",
            errorSource:
              failedObj.errorSource || failedObj.errorCode || "unknown",
          },
        };
      });
  }
}


module.exports = ClientService;
