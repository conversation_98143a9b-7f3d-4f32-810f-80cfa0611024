const express = require("express");
const router = express.Router();
const Auth = require("../_middlewares/auth");

// ----------------------------

const pclDemandletterController = require("../controllers/pcldemandletter");

module.exports=(app)=>{
    // router.post("/pclDeceasedletter",  pclDemandletterController.pclDemandletter(app));
    router.post("/pclDeceasedletter" , Auth,  pclDemandletterController.pclDemandletter(app));

    // router.post("/pclDeceasedletter-webhook", function(req, res,next ){
    //     req.loanID = req.body.loanId;
    //     req.mambuData = {
    //       loanID: req.loanID,
    //       source:"ui"
    //     };
    //     return next();
    // },   pclDemandletterController.pclDemandletter(app));


    return router;
}

